using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using ConquerBotWPF.Helpers;

namespace ConquerBotWPF.Services
{
    public class Injector
    {
        // WinAPI DLL imports
        [DllImport("kernel32.dll")]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint dwFreeType);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] buffer, int size, out IntPtr bytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] buffer, int size, out IntPtr bytesRead);

        [DllImport("kernel32.dll")]
        private static extern IntPtr CreateRemoteThread(IntPtr hProcess,
            IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress,
            IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId);

        private const uint PROCESS_ALL_ACCESS = 0x001F0FFF;
        private const uint MEM_COMMIT = 0x1000;
        private const uint MEM_RESERVE = 0x2000;
        private const uint PAGE_EXECUTE_READWRITE = 0x40;
        private const uint MEM_RELEASE = 0x8000;

        // تخزين العنوان الأصلي لحفظه عند تعطيل السكربت
        private IntPtr originalAddress = IntPtr.Zero;
        private byte[]? originalBytes = null;
        private IntPtr allocatedMemory = IntPtr.Zero;

        // النمط والبيانات الأصلية من السكربت
        private readonly byte[] pattern = new byte[] { 0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00 };
        private readonly string mask = "xxxxxxxxxx";

        private readonly KeystoneService keystone = new KeystoneService();

        public void InjectOneHit(Process process)
        {
            IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, process.Id);
            if (hProc == IntPtr.Zero)
                throw new Exception("Failed to open process.");

            // البحث عن العنوان الذي يحتوي على النمط
            IntPtr baseAddr = PatternScanner.FindPattern(process, pattern, mask);
            if (baseAddr == IntPtr.Zero)
                throw new Exception("Pattern not found.");

            // حفظ البايتات الأصلية
            originalAddress = baseAddr;
            originalBytes = new byte[pattern.Length];
            if (!ReadProcessMemory(hProc, baseAddr, originalBytes, originalBytes.Length, out _))
                throw new Exception("Failed to read original bytes.");

            // تخصيص ذاكرة للسكربت
            allocatedMemory = VirtualAllocEx(hProc, IntPtr.Zero, 0x1000, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
            if (allocatedMemory == IntPtr.Zero)
                throw new Exception("Failed to allocate memory in target process.");

            // استخدام KeystoneService للحصول على bytecode
            byte[] assembledCode = keystone.Assemble("");

            // كتابة السكربت في الذاكرة المخصصة
            if (!WriteProcessMemory(hProc, allocatedMemory, assembledCode, assembledCode.Length, out _))
                throw new Exception("Failed to write assembled code.");

            // كتابة القفز من العنوان الأصلي إلى الذاكرة الجديدة (jmp)
            byte[] jmpBytes = GetJmpBytes(baseAddr, allocatedMemory);
            if (!WriteProcessMemory(hProc, baseAddr, jmpBytes, jmpBytes.Length, out _))
                throw new Exception("Failed to write JMP to original address.");

            CloseHandle(hProc);
        }

        public void RemoveOneHit(Process process)
        {
            IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, process.Id);
            if (hProc == IntPtr.Zero)
                throw new Exception("Failed to open process.");

            // إعادة البايتات الأصلية
            if (originalAddress != IntPtr.Zero && originalBytes != null)
            {
                WriteProcessMemory(hProc, originalAddress, originalBytes, originalBytes.Length, out _);
            }

            // تحرير الذاكرة المخصصة
            if (allocatedMemory != IntPtr.Zero)
            {
                VirtualFreeEx(hProc, allocatedMemory, 0, MEM_RELEASE);
                allocatedMemory = IntPtr.Zero;
            }

            CloseHandle(hProc);
        }

        // Helper method لحساب JMP من عنوان إلى عنوان (5 bytes)
        private byte[] GetJmpBytes(IntPtr from, IntPtr to)
        {
            int offset = (int)(to.ToInt64() - from.ToInt64() - 5);
            byte[] jmp = new byte[5];
            jmp[0] = 0xE9; // JMP opcode
            BitConverter.GetBytes(offset).CopyTo(jmp, 1);
            return jmp;
        }
    }
}
