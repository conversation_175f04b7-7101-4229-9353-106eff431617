# ConquerBotWPF

تطبيق WPF لحقن سكربت One Hit في لعبة Conquer Online.

## المتطلبات

- .NET 6.0 أو أحدث
- Windows 10/11
- صلاحيات المدير (Administrator privileges)
- مكتبة Keystone.NET للتجميع

## الميزات

- عرض قائمة بجميع عمليات لعبة Conquer المفتوحة
- قراءة أسماء اللاعبين من الذاكرة
- تفعيل/إيقاف سكربت One Hit لكل لاعب على حدة
- واجهة مستخدم باللغة العربية
- حقن آمن في الذاكرة مع إمكانية الإستعادة

## كيفية الاستخدام

1. تشغيل التطبيق كمدير (Run as Administrator)
2. الضغط على "تحديث قائمة اللاعبين" لتحميل العمليات المفتوحة
3. اختيار اللاعب المطلوب من القائمة
4. تفعيل/إيقاف البوت باستخدام CheckBox

## البناء والتشغيل

```bash
# بناء المشروع باستخدام ملف الحل
dotnet build ConquerBot.sln

# تشغيل التطبيق
dotnet run --project ConquerBotWPF.csproj

# أو استخدام ملف batch المرفق
RunAsAdmin.bat
```

## ملاحظات مهمة

- يجب تشغيل التطبيق بصلاحيات المدير
- تأكد من أن لعبة Conquer مفتوحة قبل استخدام التطبيق
- السكربت يعمل على عنوان الذاكرة: `Conquer.exe+14FC7C4`
- النمط المستخدم للبحث: `81 BB 60 02 00 00 3F 42 0F 00`

## هيكل المشروع

```
ConquerBotWPF/
├── ConquerBot.sln             # ملف الحل (Solution)
├── ConquerBotWPF.csproj       # ملف المشروع
├── Models/
│   └── PlayerModel.cs         # نموذج بيانات اللاعب
├── ViewModels/                # نماذج العرض (MVVM)
│   ├── MainViewModel.cs       # ViewModel الرئيسي
│   └── RelayCommand.cs        # تنفيذ ICommand
├── Services/
│   ├── InjectionService.cs    # خدمة الحقن الرئيسية
│   ├── Injector.cs           # خدمة الحقن القديمة
│   └── KeystoneService.cs    # خدمة تجميع ASM
├── Helpers/
│   ├── MemoryHelper.cs       # مساعد قراءة الذاكرة
│   └── PatternScanner.cs     # مساعد البحث عن الأنماط
├── Converters/
│   └── BooleanConverter.cs   # محولات البيانات
├── MainWindow.xaml           # الواجهة الرئيسية
├── MainWindow.xaml.cs        # منطق الواجهة (مبسط)
├── App.xaml                  # إعدادات التطبيق
├── App.xaml.cs              # منطق التطبيق
├── app.manifest             # صلاحيات المدير
└── bin/ + obj/              # ناتج التجميع
```

## تحذيرات

- هذا التطبيق مخصص للأغراض التعليمية فقط
- استخدم على مسؤوليتك الخاصة
- قد يؤدي استخدام البوتات إلى حظر الحساب في اللعبة
