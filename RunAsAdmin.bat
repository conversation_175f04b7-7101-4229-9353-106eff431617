@echo off
echo Starting ConquerBotWPF with Administrator privileges...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges.
    echo.
) else (
    echo This script requires Administrator privileges.
    echo Please run as Administrator.
    pause
    exit /b 1
)

REM Build the project if needed
if not exist "bin\Debug\net6.0-windows\ConquerBotWPF.exe" (
    echo Building the project...
    dotnet build ConquerBot.sln
    if %errorLevel% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
)

REM Run the application
echo Starting ConquerBotWPF...
echo.
start "" "bin\Debug\net6.0-windows\ConquerBotWPF.exe"

echo Application started successfully!
pause
