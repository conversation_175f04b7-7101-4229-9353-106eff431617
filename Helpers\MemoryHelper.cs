using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace ConquerBotWPF.Helpers
{
    public static class MemoryHelper
    {
        #region WinAPI Imports

        [DllImport("kernel32.dll")]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll")]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] buffer, int size, out int bytesRead);

        #endregion

        private const uint PROCESS_VM_READ = 0x0010;
        private const uint PROCESS_QUERY_INFORMATION = 0x0400;

        public static async Task<string> ReadPlayerNameAsync(Process process, int offset)
        {
            return await Task.Run(() =>
            {
                IntPtr hProc = OpenProcess(PROCESS_VM_READ | PROCESS_QUERY_INFORMATION, false, process.Id);
                if (hProc == IntPtr.Zero)
                    return "[لا يمكن فتح العملية]";

                try
                {
                    IntPtr baseAddr = (process.MainModule?.BaseAddress ?? IntPtr.Zero) + offset;
                    byte[] buffer = new byte[30]; // نفترض الاسم لا يتجاوز 30 حرف
                    if (ReadProcessMemory(hProc, baseAddr, buffer, buffer.Length, out int bytesRead) && bytesRead > 0)
                    {
                        string name = Encoding.ASCII.GetString(buffer);
                        int nullIndex = name.IndexOf('\0');
                        if (nullIndex >= 0)
                            name = name.Substring(0, nullIndex);
                        return name;
                    }
                    return "[خطأ في القراءة]";
                }
                finally
                {
                    CloseHandle(hProc);
                }
            });
        }
    }
}
