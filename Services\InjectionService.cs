using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using ConquerBotWPF.Helpers;

namespace ConquerBotWPF.Services
{
    public class InjectionService
    {
        #region WinAPI Imports

        [DllImport("kernel32.dll")]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualFreeEx(IntPtr hProcess, IntPtr lpAddress,
            uint dwSize, uint dwFreeType);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] buffer, int size, out IntPtr bytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] buffer, int size, out IntPtr bytesRead);

        #endregion

        #region Constants

        private const uint PROCESS_ALL_ACCESS = 0x001F0FFF;
        private const uint MEM_COMMIT = 0x1000;
        private const uint MEM_RESERVE = 0x2000;
        private const uint PAGE_EXECUTE_READWRITE = 0x40;
        private const uint MEM_RELEASE = 0x8000;

        #endregion

        #region Private Fields

        private readonly byte[] _pattern = { 0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00 };
        private readonly string _mask = "xxxxxxxxxx";
        private readonly KeystoneService _keystoneService;

        // تخزين حالة الحقن لكل عملية
        private readonly Dictionary<int, InjectionState> _injectionStates = new();

        #endregion

        public InjectionService()
        {
            _keystoneService = new KeystoneService();
        }

        #region Public Methods

        public void InjectOneHit(Process process)
        {
            if (process == null)
                throw new ArgumentNullException(nameof(process));

            var processId = process.Id;

            // التحقق من وجود حقن سابق
            if (_injectionStates.ContainsKey(processId))
            {
                throw new InvalidOperationException("البوت مفعل بالفعل لهذا اللاعب");
            }

            IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, processId);
            if (hProc == IntPtr.Zero)
                throw new Exception("فشل في فتح العملية. تأكد من تشغيل التطبيق بصلاحيات المدير.");

            try
            {
                // البحث عن النمط
                IntPtr targetAddress = PatternScanner.FindPattern(process, _pattern, _mask);
                if (targetAddress == IntPtr.Zero)
                    throw new Exception("لم يتم العثور على النمط المطلوب. قد يكون إصدار اللعبة غير مدعوم.");

                // إنشاء حالة حقن جديدة
                var injectionState = new InjectionState
                {
                    ProcessId = processId,
                    OriginalAddress = targetAddress,
                    ProcessHandle = hProc
                };

                // حفظ البايتات الأصلية
                injectionState.OriginalBytes = new byte[_pattern.Length];
                if (!ReadProcessMemory(hProc, targetAddress, injectionState.OriginalBytes, 
                    injectionState.OriginalBytes.Length, out _))
                {
                    throw new Exception("فشل في قراءة البايتات الأصلية.");
                }

                // تخصيص ذاكرة للكود المحقون
                injectionState.AllocatedMemory = VirtualAllocEx(hProc, IntPtr.Zero, 0x1000, 
                    MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
                if (injectionState.AllocatedMemory == IntPtr.Zero)
                    throw new Exception("فشل في تخصيص الذاكرة.");

                // الحصول على الكود المجمع
                byte[] assembledCode = _keystoneService.Assemble("");

                // كتابة الكود في الذاكرة المخصصة
                if (!WriteProcessMemory(hProc, injectionState.AllocatedMemory, assembledCode, 
                    assembledCode.Length, out _))
                {
                    VirtualFreeEx(hProc, injectionState.AllocatedMemory, 0, MEM_RELEASE);
                    throw new Exception("فشل في كتابة الكود المجمع.");
                }

                // إنشاء قفزة من الموقع الأصلي إلى الكود المحقون
                byte[] jumpBytes = CreateJumpBytes(targetAddress, injectionState.AllocatedMemory);
                if (!WriteProcessMemory(hProc, targetAddress, jumpBytes, jumpBytes.Length, out _))
                {
                    VirtualFreeEx(hProc, injectionState.AllocatedMemory, 0, MEM_RELEASE);
                    throw new Exception("فشل في كتابة القفزة.");
                }

                // حفظ حالة الحقن
                _injectionStates[processId] = injectionState;
            }
            catch
            {
                CloseHandle(hProc);
                throw;
            }
        }

        public void RemoveInjection(Process process)
        {
            if (process == null)
                throw new ArgumentNullException(nameof(process));

            var processId = process.Id;

            if (!_injectionStates.TryGetValue(processId, out var injectionState))
            {
                throw new InvalidOperationException("لا يوجد حقن مفعل لهذا اللاعب");
            }

            try
            {
                // استعادة البايتات الأصلية
                if (!WriteProcessMemory(injectionState.ProcessHandle, injectionState.OriginalAddress,
                    injectionState.OriginalBytes, injectionState.OriginalBytes.Length, out _))
                {
                    throw new Exception("فشل في استعادة البايتات الأصلية.");
                }

                // تحرير الذاكرة المخصصة
                VirtualFreeEx(injectionState.ProcessHandle, injectionState.AllocatedMemory, 0, MEM_RELEASE);

                // إغلاق مقبض العملية
                CloseHandle(injectionState.ProcessHandle);

                // إزالة حالة الحقن
                _injectionStates.Remove(processId);
            }
            catch
            {
                // في حالة الخطأ، نحاول على الأقل تنظيف الموارد
                CloseHandle(injectionState.ProcessHandle);
                _injectionStates.Remove(processId);
                throw;
            }
        }

        public bool IsInjected(Process process)
        {
            return process != null && _injectionStates.ContainsKey(process.Id);
        }

        #endregion

        #region Private Methods

        private static byte[] CreateJumpBytes(IntPtr from, IntPtr to)
        {
            // حساب المسافة النسبية للقفزة
            long offset = to.ToInt64() - from.ToInt64() - 5; // 5 هو حجم تعليمة JMP

            var jumpBytes = new byte[5];
            jumpBytes[0] = 0xE9; // JMP opcode
            BitConverter.GetBytes((int)offset).CopyTo(jumpBytes, 1);

            return jumpBytes;
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            // تنظيف جميع الحقن المفعلة
            foreach (var injectionState in _injectionStates.Values)
            {
                try
                {
                    WriteProcessMemory(injectionState.ProcessHandle, injectionState.OriginalAddress,
                        injectionState.OriginalBytes, injectionState.OriginalBytes.Length, out _);
                    VirtualFreeEx(injectionState.ProcessHandle, injectionState.AllocatedMemory, 0, MEM_RELEASE);
                    CloseHandle(injectionState.ProcessHandle);
                }
                catch
                {
                    // تجاهل الأخطاء أثناء التنظيف
                }
            }
            _injectionStates.Clear();
        }

        #endregion

        #region Nested Classes

        private class InjectionState
        {
            public int ProcessId { get; set; }
            public IntPtr ProcessHandle { get; set; }
            public IntPtr OriginalAddress { get; set; }
            public byte[] OriginalBytes { get; set; } = Array.Empty<byte>();
            public IntPtr AllocatedMemory { get; set; }
        }

        #endregion
    }
}
