# التفاصيل التقنية - ConquerBotWPF

## نظرة عامة على التقنيات المستخدمة

### 1. حقن الذاكرة (Memory Injection)

#### العملية الأساسية:
1. **البحث عن النمط (Pattern Scanning)**
   - البحث عن النمط: `81 BB 60 02 00 00 3F 42 0F 00`
   - استخدام mask: `"xxxxxxxxxx"`
   - البحث في ذاكرة العملية الرئيسية

2. **تخصيص الذاكرة**
   - استخدام `VirtualAllocEx` لتخصيص ذاكرة جديدة
   - حجم الذاكرة: 0x1000 bytes (4KB)
   - صلاحيات: `PAGE_EXECUTE_READWRITE`

3. **كتابة الكود**
   - حفظ البايتات الأصلية
   - كتابة الكود الجديد في الذاكرة المخصصة
   - إنشاء JMP من الموقع الأصلي إلى الكود الجديد

### 2. Windows API المستخدمة

#### kernel32.dll Functions:
```csharp
OpenProcess()           // فتح العملية
ReadProcessMemory()     // قراءة الذاكرة
WriteProcessMemory()    // كتابة الذاكرة
VirtualAllocEx()        // تخصيص ذاكرة
VirtualFreeEx()         // تحرير الذاكرة
CloseHandle()           // إغلاق المقبض
```

#### Process Access Rights:
- `PROCESS_ALL_ACCESS` (0x001F0FFF)
- `PROCESS_VM_READ` (0x0010)
- `PROCESS_QUERY_INFORMATION` (0x0400)

### 3. بنية الكود المحقون

#### الهدف من السكربت:
- تفعيل ميزة "One Hit" في اللعبة
- تعديل قيم الضرر في الذاكرة
- التحكم في حالة الوحوش والشخصية

#### العناوين المستهدفة:
```
Conquer.exe+14FC7C4  - عنوان اسم اللاعب
Conquer.exe+14F1D80  - عنوان البيانات الرئيسية
```

#### البايتات المستبدلة:
```assembly
Original: 81 BB 60 02 00 00 3F 42 0F 00
Modified: E9 XX XX XX XX (JMP to injected code)
```

### 4. هيكل البيانات

#### PlayerModel:
```csharp
public class PlayerModel
{
    public int PID { get; set; }           // معرف العملية
    public string Name { get; set; }       // اسم اللاعب
    public Process Process { get; set; }   // كائن العملية
}
```

#### Injector State:
```csharp
private IntPtr originalAddress;    // العنوان الأصلي
private byte[] originalBytes;      // البايتات الأصلية
private IntPtr allocatedMemory;    // الذاكرة المخصصة
```

### 5. آلية الأمان

#### حفظ الحالة الأصلية:
- حفظ البايتات الأصلية قبل التعديل
- إمكانية الاستعادة الكاملة
- تحرير الذاكرة المخصصة عند الإيقاف

#### معالجة الأخطاء:
- التحقق من صحة العملية
- التحقق من نجاح تخصيص الذاكرة
- التحقق من نجاح عمليات القراءة/الكتابة

### 6. الأداء والتحسين

#### تحسينات الذاكرة:
- استخدام `async/await` لعمليات الذاكرة
- تحرير الموارد في `finally` blocks
- استخدام `using` statements حيث أمكن

#### تحسينات الواجهة:
- `ObservableCollection` للتحديث التلقائي
- `Task.Run` للعمليات الثقيلة
- معالجة الاستثناءات مع رسائل واضحة

### 7. قيود النظام

#### متطلبات الأمان:
- صلاحيات المدير مطلوبة
- Windows Defender قد يحجب التطبيق
- بعض برامج مكافحة الفيروسات قد تعتبره تهديد

#### قيود التوافق:
- يعمل فقط مع إصدارات محددة من اللعبة
- العناوين قد تتغير مع التحديثات
- النمط قد يختلف بين الإصدارات

### 8. استكشاف الأخطاء المتقدم

#### أدوات التشخيص:
```csharp
// فحص العملية
Process.GetProcessesByName("Conquer")

// فحص الذاكرة
ReadProcessMemory(hProcess, address, buffer, size, out bytesRead)

// فحص النمط
PatternScanner.FindPattern(process, pattern, mask)
```

#### رسائل الخطأ الشائعة:
- "Failed to open process" → مشكلة في الصلاحيات
- "Pattern not found" → النمط غير موجود أو متغير
- "Failed to allocate memory" → مشكلة في الذاكرة

### 9. الاعتبارات الأخلاقية والقانونية

#### الاستخدام المسؤول:
- للأغراض التعليمية فقط
- احترام شروط خدمة اللعبة
- عدم استخدامه في البيئات التنافسية

#### المخاطر:
- إمكانية حظر الحساب
- مشاكل في استقرار اللعبة
- مخاطر أمنية محتملة
