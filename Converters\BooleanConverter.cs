using System;
using System.Globalization;
using System.Windows.Data;

namespace ConquerBotWPF
{
    public class BooleanConverter : IValueConverter
    {
        public static readonly BooleanConverter Invert = new() { InvertValue = true };
        public static readonly BooleanConverter IsNotNull = new() { CheckNotNull = true };

        public bool InvertValue { get; set; }
        public bool CheckNotNull { get; set; }

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (CheckNotNull)
            {
                bool result = value != null;
                return InvertValue ? !result : result;
            }

            if (value is bool boolValue)
            {
                return InvertValue ? !boolValue : boolValue;
            }

            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return InvertValue ? !boolValue : boolValue;
            }

            return false;
        }
    }
}
