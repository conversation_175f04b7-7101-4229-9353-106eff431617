using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ConquerBotWPF.Models;
using ConquerBotWPF.Services;
using ConquerBotWPF.Helpers;

namespace ConquerBotWPF.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly SafeInjectionService _injectionService;
        private bool _isBotEnabled;
        private PlayerModel? _selectedPlayer;
        private bool _isRefreshing;

        public MainViewModel()
        {
            _injectionService = new SafeInjectionService();
            Players = new ObservableCollection<PlayerModel>();
            
            RefreshPlayersCommand = new RelayCommand(async () => await RefreshPlayersAsync());
            
            // تحديث أولي للقائمة
            _ = RefreshPlayersAsync();
        }

        #region Properties

        public ObservableCollection<PlayerModel> Players { get; }

        public PlayerModel? SelectedPlayer
        {
            get => _selectedPlayer;
            set
            {
                if (SetProperty(ref _selectedPlayer, value))
                {
                    // تحديث حالة البوت بناءً على اللاعب المحدد
                    UpdateBotStatus();
                }
            }
        }

        public bool IsBotEnabled
        {
            get => _isBotEnabled;
            set
            {
                if (SetProperty(ref _isBotEnabled, value))
                {
                    // تنفيذ العملية عند تغيير الحالة
                    _ = HandleBotToggleAsync(value);
                }
            }
        }

        public bool IsRefreshing
        {
            get => _isRefreshing;
            set => SetProperty(ref _isRefreshing, value);
        }

        #endregion

        #region Commands

        public ICommand RefreshPlayersCommand { get; }

        #endregion

        #region Methods

        private async Task RefreshPlayersAsync()
        {
            try
            {
                IsRefreshing = true;
                IsBotEnabled = false;
                Players.Clear();

                var processes = Process.GetProcessesByName("Conquer");

                foreach (var process in processes)
                {
                    try
                    {
                        string playerName = await MemoryHelper.ReadPlayerNameAsync(process, 0x14FC7C4);
                        Players.Add(new PlayerModel 
                        { 
                            Process = process, 
                            PID = process.Id, 
                            Name = playerName 
                        });
                    }
                    catch (Exception ex)
                    {
                        // تسجيل الخطأ وتجاهل هذه العملية
                        Debug.WriteLine($"Error reading player name for PID {process.Id}: {ex.Message}");
                    }
                }

                if (Players.Count > 0)
                {
                    SelectedPlayer = Players[0];
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث قائمة اللاعبين: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsRefreshing = false;
            }
        }



        private void UpdateBotStatus()
        {
            if (SelectedPlayer != null)
            {
                // تحديث حالة البوت بناءً على حالة الحقن الفعلية
                var currentStatus = _injectionService.IsInjected(SelectedPlayer.Process);
                if (_isBotEnabled != currentStatus)
                {
                    _isBotEnabled = currentStatus;
                    OnPropertyChanged(nameof(IsBotEnabled));
                }
            }
            else
            {
                if (_isBotEnabled)
                {
                    _isBotEnabled = false;
                    OnPropertyChanged(nameof(IsBotEnabled));
                }
            }
        }

        private async Task HandleBotToggleAsync(bool enable)
        {
            if (SelectedPlayer == null) return;

            try
            {
                if (enable)
                {
                    // تفعيل البوت
                    await Task.Run(() => _injectionService.InjectOneHit(SelectedPlayer.Process));

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"تم تفعيل البوت للاعب {SelectedPlayer.Name}",
                            "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    });
                }
                else
                {
                    // إيقاف البوت
                    if (_injectionService.IsInjected(SelectedPlayer.Process))
                    {
                        await Task.Run(() =>
                        {
                            try
                            {
                                _injectionService.RemoveInjection(SelectedPlayer.Process);
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error removing injection: {ex.Message}");
                            }
                        });

                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show($"تم إيقاف البوت للاعب {SelectedPlayer.Name}",
                                "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"حدث خطأ: {ex.Message}",
                        "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                });

                // إعادة تعيين الحالة في حالة الخطأ
                _isBotEnabled = !enable;
                OnPropertyChanged(nameof(IsBotEnabled));
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
