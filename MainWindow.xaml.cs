using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ConquerBotWPF.Models;
using ConquerBotWPF.Services;
using ConquerBotWPF.Helpers;

namespace ConquerBotWPF
{
    public partial class MainWindow : Window
    {
        private ObservableCollection<PlayerModel> Players = new ObservableCollection<PlayerModel>();
        private Injector injector = new Injector();
        
        public MainWindow()
        {
            InitializeComponent();
            PlayersListBox.ItemsSource = Players;
            _ = RefreshPlayersList();
        }

        private async void RefreshPlayersButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshPlayersList();
        }

        private async Task RefreshPlayersList()
        {
            EnableBotCheckBox.IsEnabled = false;
            EnableBotCheckBox.IsChecked = false;
            Players.Clear();

            var processes = Process.GetProcessesByName("Conquer");

            foreach (var p in processes)
            {
                string playerName = await MemoryHelper.ReadPlayerNameAsync(p, 0x14FC7C4); // العنوان المقدم
                Players.Add(new PlayerModel { Process = p, PID = p.Id, Name = playerName });
            }

            if (Players.Count > 0)
            {
                PlayersListBox.SelectedIndex = 0;
                EnableBotCheckBox.IsEnabled = true;
            }
        }

        private async void EnableBotCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (PlayersListBox.SelectedItem is PlayerModel player)
            {
                try
                {
                    await Task.Run(() => injector.InjectOneHit(player.Process));
                    MessageBox.Show($"تم تفعيل البوت للاعب {player.Name}", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء التفعيل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    EnableBotCheckBox.IsChecked = false;
                }
            }
        }

        private async void EnableBotCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (PlayersListBox.SelectedItem is PlayerModel player)
            {
                try
                {
                    await Task.Run(() => injector.RemoveOneHit(player.Process));
                    MessageBox.Show($"تم إيقاف البوت للاعب {player.Name}", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء الإيقاف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    EnableBotCheckBox.IsChecked = true;
                }
            }
        }
    }
}
