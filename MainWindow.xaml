<Window x:Class="ConquerBotWPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Conquer Bot Controller" Height="450" Width="700" Background="#FF1E1E1E"
        >
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Text="قائمة اللاعبين" Foreground="White" FontSize="20" FontWeight="Bold"/>

        <ListBox x:Name="PlayersListBox" Grid.Row="1" Margin="0,10" Background="#FF2D2D30" Foreground="White" BorderThickness="0"
                 SelectionMode="Single" DisplayMemberPath="Name"/>

        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0" >
            <CheckBox x:Name="EnableBotCheckBox" Content="تفعيل البوت" Foreground="White" FontSize="16" Margin="0,0,20,0"
                      IsEnabled="False" Checked="EnableBotCheckBox_Checked" Unchecked="EnableBotCheckBox_Unchecked"/>
            <Button Content="تحديث قائمة اللاعبين" Click="RefreshPlayersButton_Click" Width="150" Height="30"/>
        </StackPanel>
    </Grid>
</Window>
