<Window x:Class="ConquerBotWPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:ConquerBotWPF.ViewModels"
        xmlns:local="clr-namespace:ConquerBotWPF"
        Title="Conquer Bot Controller" Height="450" Width="700" Background="#FF1E1E1E"
        >
    <Window.DataContext>
        <vm:MainViewModel />
    </Window.DataContext>
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Text="قائمة اللاعبين" Foreground="White" FontSize="20" FontWeight="Bold"/>

        <ListBox Grid.Row="1" Margin="0,10" Background="#FF2D2D30" Foreground="White" BorderThickness="0"
                 SelectionMode="Single" DisplayMemberPath="Name"
                 ItemsSource="{Binding Players}"
                 SelectedItem="{Binding SelectedPlayer}"/>

        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0" >
            <CheckBox Content="تفعيل البوت" Foreground="White" FontSize="16" Margin="0,0,20,0"
                      IsChecked="{Binding IsBotEnabled, Mode=TwoWay}"
                      IsEnabled="{Binding SelectedPlayer, Converter={x:Static local:BooleanConverter.IsNotNull}}"/>
            <Button Content="تحديث قائمة اللاعبين"
                    Command="{Binding RefreshPlayersCommand}"
                    IsEnabled="{Binding IsRefreshing, Converter={x:Static local:BooleanConverter.Invert}}"
                    Width="150" Height="30"/>
        </StackPanel>
    </Grid>
</Window>
