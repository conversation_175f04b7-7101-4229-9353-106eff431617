using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace ConquerBotWPF.Helpers
{
    public static class PatternScanner
    {
        [DllImport("kernel32.dll")]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] buffer, int size, out int bytesRead);

        [DllImport("kernel32.dll")]
        private static extern bool CloseHandle(IntPtr hObject);

        private const uint PROCESS_VM_READ = 0x0010;
        private const uint PROCESS_QUERY_INFORMATION = 0x0400;

        public static IntPtr FindPattern(Process process, byte[] pattern, string mask)
        {
            IntPtr hProc = OpenProcess(PROCESS_VM_READ | PROCESS_QUERY_INFORMATION, false, process.Id);
            if (hProc == IntPtr.Zero) return IntPtr.Zero;

            try
            {
                var baseAddr = process.MainModule?.BaseAddress ?? IntPtr.Zero;
                var moduleSize = process.MainModule?.ModuleMemorySize ?? 0;

                byte[] moduleBytes = new byte[moduleSize];
                if (!ReadProcessMemory(hProc, baseAddr, moduleBytes, moduleSize, out int bytesRead) || bytesRead != moduleSize)
                    return IntPtr.Zero;

                for (int i = 0; i < moduleSize - pattern.Length; i++)
                {
                    bool found = true;
                    for (int j = 0; j < pattern.Length; j++)
                    {
                        if (mask[j] == 'x' && pattern[j] != moduleBytes[i + j])
                        {
                            found = false;
                            break;
                        }
                    }
                    if (found)
                    {
                        return IntPtr.Add(baseAddr, i);
                    }
                }
            }
            finally
            {
                CloseHandle(hProc);
            }
            return IntPtr.Zero;
        }
    }
}
