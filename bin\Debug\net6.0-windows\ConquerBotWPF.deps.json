{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"ConquerBotWPF/1.0.0": {"dependencies": {"Iced": "1.21.0"}, "runtime": {"ConquerBotWPF.dll": {}}}, "Iced/1.21.0": {"runtime": {"lib/netstandard2.1/Iced.dll": {"assemblyVersion": "1.21.0.0", "fileVersion": "1.21.0.0"}}}}}, "libraries": {"ConquerBotWPF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Iced/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-dv5+81Q1TBQvVMSOOOmRcjJmvWcX3BZPZsIq31+RLc5cNft0IHAyNlkdb7ZarOWG913PyBoFDsDXoCIlKmLclg==", "path": "iced/1.21.0", "hashPath": "iced.1.21.0.nupkg.sha512"}}}