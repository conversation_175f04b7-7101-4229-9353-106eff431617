using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using ConquerBotWPF.Helpers;

namespace ConquerBotWPF.Services
{
    public class SafeInjectionService
    {
        #region WinAPI Imports

        [DllImport("kernel32.dll")]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll")]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] buffer, int size, out IntPtr bytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            byte[] buffer, int size, out IntPtr bytesRead);

        #endregion

        #region Constants

        private const uint PROCESS_ALL_ACCESS = 0x001F0FFF;

        #endregion

        #region Private Fields

        // النمط الكامل من السكربت الأصلي
        private readonly byte[] _pattern = { 0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00, 0x77, 0x0D };
        private readonly string _mask = "xxxxxxxxxxxx";

        // تخزين حالة الحقن لكل عملية
        private readonly Dictionary<int, InjectionState> _injectionStates = new();

        #endregion

        public SafeInjectionService()
        {
        }

        #region Public Methods

        public void InjectOneHit(Process process)
        {
            if (process == null)
                throw new ArgumentNullException(nameof(process));

            var processId = process.Id;

            // التحقق من وجود حقن سابق
            if (_injectionStates.ContainsKey(processId))
            {
                RemoveInjection(process);
            }

            IntPtr hProc = OpenProcess(PROCESS_ALL_ACCESS, false, processId);
            if (hProc == IntPtr.Zero)
                throw new Exception("فشل في فتح العملية. تأكد من تشغيل التطبيق بصلاحيات المدير.");

            try
            {
                // البحث عن النمط
                IntPtr targetAddress = PatternScanner.FindPattern(process, _pattern, _mask);
                if (targetAddress == IntPtr.Zero)
                    throw new Exception("لم يتم العثور على النمط المطلوب. قد يكون إصدار اللعبة غير مدعوم.");

                // إنشاء حالة حقن جديدة
                var injectionState = new InjectionState
                {
                    ProcessId = processId,
                    OriginalAddress = targetAddress,
                    ProcessHandle = hProc
                };

                // حفظ البايتات الأصلية
                injectionState.OriginalBytes = new byte[_pattern.Length];
                if (!ReadProcessMemory(hProc, targetAddress, injectionState.OriginalBytes, 
                    injectionState.OriginalBytes.Length, out _))
                {
                    throw new Exception("فشل في قراءة البايتات الأصلية.");
                }

                // إنشاء الكود المعدل - نسخة مبسطة وآمنة
                byte[] modifiedCode = CreateSafeModifiedCode();

                // التأكد من أن حجم الكود المعدل لا يتجاوز حجم النمط الأصلي
                if (modifiedCode.Length > _pattern.Length)
                {
                    throw new Exception("حجم الكود المعدل أكبر من النمط الأصلي.");
                }

                // إضافة NOP instructions إذا لزم الأمر
                var finalCode = new byte[_pattern.Length];
                Array.Copy(modifiedCode, finalCode, modifiedCode.Length);
                for (int i = modifiedCode.Length; i < _pattern.Length; i++)
                {
                    finalCode[i] = 0x90; // NOP
                }

                // كتابة الكود المعدل
                if (!WriteProcessMemory(hProc, targetAddress, finalCode, finalCode.Length, out _))
                {
                    throw new Exception("فشل في كتابة الكود المعدل.");
                }

                // حفظ حالة الحقن
                _injectionStates[processId] = injectionState;
            }
            catch
            {
                CloseHandle(hProc);
                throw;
            }
        }

        public void RemoveInjection(Process process)
        {
            if (process == null)
                throw new ArgumentNullException(nameof(process));

            var processId = process.Id;

            if (!_injectionStates.TryGetValue(processId, out var injectionState))
            {
                return; // لا يوجد حقن مفعل
            }

            try
            {
                // استعادة البايتات الأصلية
                if (!WriteProcessMemory(injectionState.ProcessHandle, injectionState.OriginalAddress,
                    injectionState.OriginalBytes, injectionState.OriginalBytes.Length, out _))
                {
                    throw new Exception("فشل في استعادة البايتات الأصلية.");
                }

                // إغلاق مقبض العملية
                CloseHandle(injectionState.ProcessHandle);

                // إزالة حالة الحقن
                _injectionStates.Remove(processId);
            }
            catch
            {
                CloseHandle(injectionState.ProcessHandle);
                _injectionStates.Remove(processId);
                throw;
            }
        }

        public bool IsInjected(Process process)
        {
            return process != null && _injectionStates.ContainsKey(process.Id);
        }

        #endregion

        #region Private Methods

        private byte[] CreateSafeModifiedCode()
        {
            // كود مبسط وآمن - يحافظ على التعليمة الأصلية مع تعديل بسيط
            var bytes = new List<byte>();

            // التعليمة الأصلية: cmp [ebx+00000260],000F423F
            bytes.AddRange(new byte[] { 0x81, 0xBB, 0x60, 0x02, 0x00, 0x00, 0x3F, 0x42, 0x0F, 0x00 });

            // تعديل الشرط: بدلاً من ja، نستخدم jmp لتجاهل الفحص
            // jmp +2 (تجاهل التعليمة التالية)
            bytes.AddRange(new byte[] { 0xEB, 0x00 }); // jmp +0 (لا تفعل شيئاً)

            return bytes.ToArray();
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            foreach (var injectionState in _injectionStates.Values)
            {
                try
                {
                    WriteProcessMemory(injectionState.ProcessHandle, injectionState.OriginalAddress,
                        injectionState.OriginalBytes, injectionState.OriginalBytes.Length, out _);
                    CloseHandle(injectionState.ProcessHandle);
                }
                catch
                {
                    // تجاهل الأخطاء أثناء التنظيف
                }
            }
            _injectionStates.Clear();
        }

        #endregion

        #region Nested Classes

        private class InjectionState
        {
            public int ProcessId { get; set; }
            public IntPtr ProcessHandle { get; set; }
            public IntPtr OriginalAddress { get; set; }
            public byte[] OriginalBytes { get; set; } = Array.Empty<byte>();
        }

        #endregion
    }
}
