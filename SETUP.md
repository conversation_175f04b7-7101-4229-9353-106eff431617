# إعداد وتشغيل ConquerBotWPF

## المتطلبات الأساسية

1. **Windows 10/11**
2. **.NET 6.0 Runtime أو أحدث**
   - يمكن تحميله من: https://dotnet.microsoft.com/download/dotnet/6.0
3. **Visual Studio 2022** أو **Visual Studio Code** (اختياري للتطوير)
4. **صلاحيات المدير** (Administrator privileges)

## خطوات التشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd ConquerInjector
```

### 2. بناء المشروع
```bash
dotnet restore
dotnet build
```

### 3. تشغيل التطبيق
```bash
dotnet run
```

أو يمكنك تشغيل الملف التنفيذي مباشرة:
```bash
.\bin\Debug\net6.0-windows\ConquerBotWPF.exe
```

## ملاحظات مهمة

### صلاحيات المدير
- يج<PERSON> تشغيل التطبيق بصلاحيات المدير (Run as Administrator)
- هذا مطلوب للوصول إلى ذاكرة عمليات اللعبة

### متطلبات اللعبة
- تأكد من أن لعبة Conquer Online مفتوحة قبل تشغيل التطبيق
- يجب أن تكون العملية باسم "Conquer.exe"

### استكشاف الأخطاء

#### خطأ "Failed to open process"
- تأكد من تشغيل التطبيق بصلاحيات المدير
- تأكد من أن اللعبة مفتوحة

#### خطأ "Pattern not found"
- قد يكون عنوان الذاكرة مختلف في إصدار اللعبة الحالي
- تحقق من صحة النمط المستخدم في البحث

#### خطأ "Failed to allocate memory"
- تأكد من وجود ذاكرة كافية
- أعد تشغيل التطبيق واللعبة

## البنية التقنية

### الملفات الرئيسية
- `MainWindow.xaml` - الواجهة الرئيسية
- `Services/Injector.cs` - منطق الحقن في الذاكرة
- `Helpers/MemoryHelper.cs` - مساعد قراءة الذاكرة
- `Helpers/PatternScanner.cs` - البحث عن الأنماط

### المكتبات المستخدمة
- **Iced** - لتجميع وتفكيك التعليمات
- **WPF** - واجهة المستخدم
- **Windows API** - للوصول إلى الذاكرة

## الأمان والقانونية

⚠️ **تحذير مهم**: 
- هذا التطبيق مخصص للأغراض التعليمية فقط
- استخدام البوتات قد يؤدي إلى حظر الحساب في اللعبة
- استخدم على مسؤوليتك الخاصة
- تأكد من الامتثال لشروط خدمة اللعبة

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من اتباع جميع الخطوات بالترتيب
2. تحقق من أن جميع المتطلبات متوفرة
3. راجع ملف README.md للمزيد من التفاصيل
